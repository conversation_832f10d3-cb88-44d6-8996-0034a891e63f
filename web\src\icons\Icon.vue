<template>
  <span 
    class="icon-wrapper"
    :class="{ 'icon-clickable': clickable }"
    :style="iconStyle"
    v-html="iconSvg"
    @click="handleClick"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { getIconSvg, hasIcon, type IconName } from './index';
import { getCachedIcon, setCachedIcon } from './cache';
import { getConfig } from './config';

interface Props {
  name: IconName;
  size?: string | number;
  color?: string;
  clickable?: boolean;
}

interface Emits {
  (e: 'click', event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: '1em',
  color: 'currentColor',
  clickable: false,
});

const emit = defineEmits<Emits>();

// 计算图标SVG
const iconSvg = computed(() => {
  const config = getConfig();

  if (!hasIcon(props.name)) {
    if (config.showWarnings) {
      console.warn(`Icon "${props.name}" not found`);
    }
    return '';
  }

  // 尝试从缓存获取
  const cacheOptions = {
    size: props.size,
    color: props.color,
  };

  const cached = getCachedIcon(props.name, cacheOptions);
  if (cached && typeof cached === 'string') {
    return cached;
  }

  let svg = getIconSvg(props.name);

  // 替换SVG中的颜色和尺寸
  if (props.color !== 'currentColor') {
    svg = svg.replace(/fill="currentColor"/g, `fill="${props.color}"`);
    svg = svg.replace(/stroke="currentColor"/g, `stroke="${props.color}"`);
  }

  // 设置尺寸
  const sizeValue = typeof props.size === 'number' ? `${props.size}px` : props.size;
  svg = svg.replace(/width="[^"]*"/g, `width="${sizeValue}"`);
  svg = svg.replace(/height="[^"]*"/g, `height="${sizeValue}"`);

  // 缓存处理后的SVG
  setCachedIcon(props.name, svg, cacheOptions);

  return svg;
});

// 计算样式
const iconStyle = computed(() => {
  const sizeValue = typeof props.size === 'number' ? `${props.size}px` : props.size;
  return {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: sizeValue,
    height: sizeValue,
    color: props.color,
    cursor: props.clickable ? 'pointer' : 'inherit',
  };
});

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (props.clickable) {
    emit('click', event);
  }
};
</script>

<style scoped>
.icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.icon-wrapper :deep(svg) {
  display: block;
  width: 100%;
  height: 100%;
}

.icon-clickable {
  transition: opacity 0.2s ease;
}

.icon-clickable:hover {
  opacity: 0.8;
}

.icon-clickable:active {
  opacity: 0.6;
}
</style>
