{"name": "@shenmo/icons", "version": "1.0.0", "description": "统一的图标系统，支持Vue组件使用和TS/JS中的字符串使用", "main": "main.ts", "types": "types.ts", "files": ["*.ts", "*.vue", "*.md", "*.js"], "keywords": ["icons", "vue", "typescript", "svg", "ui", "components"], "author": "Shenmo Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/shenmo/icons.git"}, "bugs": {"url": "https://github.com/shenmo/icons/issues"}, "homepage": "https://github.com/shenmo/icons#readme", "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@types/node": "^18.0.0", "typescript": "^4.9.0", "vue": "^3.3.0"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "migrate": "node migrate.js", "test": "echo \"No tests specified\" && exit 0"}, "exports": {".": {"import": "./main.ts", "require": "./main.ts", "types": "./types.ts"}, "./Icon.vue": "./Icon.vue", "./VIcon.vue": "./VIcon.vue", "./IconDemo.vue": "./IconDemo.vue", "./IconBrowser.vue": "./IconBrowser.vue"}}