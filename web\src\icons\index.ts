// 统一图标系统
// 支持Vue组件使用和TS/JS中的字符串使用

// 图标SVG字符串映射
export const iconSvgs = {
  // Fluent Icons
  'text-header-1': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 3.5a.5.5 0 0 1 1 0V9h5V3.5a.5.5 0 0 1 1 0v13a.5.5 0 0 1-1 0V10H4v6.5a.5.5 0 0 1-1 0v-13Zm11.5 0a.5.5 0 0 1 .5.5v12.5a.5.5 0 0 1-1 0V5h-1a.5.5 0 0 1 0-1h1.5Z" fill="currentColor"/></svg>',
  
  'text-header-2': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 3.5a.5.5 0 0 1 1 0V9h5V3.5a.5.5 0 0 1 1 0v13a.5.5 0 0 1-1 0V10H4v6.5a.5.5 0 0 1-1 0v-13Zm11.5 1a2 2 0 0 1 2 2v1a2 2 0 0 1-1.106 1.789L13.618 10.5H16.5a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.4-.8l3.5-4.667A1 1 0 0 0 15.5 5.5v-1a1 1 0 0 0-1-1h-1a1 1 0 0 0-1 1v.5a.5.5 0 0 1-1 0v-.5a2 2 0 0 1 2-2h1Z" fill="currentColor"/></svg>',
  
  'text-header-3': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 3.5a.5.5 0 0 1 1 0V9h5V3.5a.5.5 0 0 1 1 0v13a.5.5 0 0 1-1 0V10H4v6.5a.5.5 0 0 1-1 0v-13Zm11.5 1a2 2 0 0 1 2 2v1a2 2 0 0 1-.5 1.323v.354a2 2 0 0 1 .5 1.323v1a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-.5a.5.5 0 0 1 1 0v.5a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-1a1 1 0 0 0-1-1h-.5a.5.5 0 0 1 0-1h.5a1 1 0 0 0 1-1v-1a1 1 0 0 0-1-1h-1a1 1 0 0 0-1 1v.5a.5.5 0 0 1-1 0v-.5a2 2 0 0 1 2-2h1Z" fill="currentColor"/></svg>',
  
  'text-bold': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 3A1.5 1.5 0 0 0 4 4.5v11A1.5 1.5 0 0 0 5.5 17h4a4.5 4.5 0 0 0 2.7-8.1A3.5 3.5 0 0 0 10.5 3h-5ZM6 5h4.5a1.5 1.5 0 0 1 0 3H6V5Zm0 5h5a2.5 2.5 0 0 1 0 5H6v-5Z" fill="currentColor"/></svg>',
  
  'text-italic': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 3a.5.5 0 0 0 0 1h1.646l-2.5 12H6a.5.5 0 0 0 0 1h4a.5.5 0 0 0 0-1H8.354l2.5-12H12a.5.5 0 0 0 0-1H8Z" fill="currentColor"/></svg>',
  
  'text-underline': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 3.75a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5H8v6.75a4 4 0 0 0 8 0V4.5h-1.25a.75.75 0 0 1 0-1.5h2.5a.75.75 0 0 1 0 1.5H16v6.75a5.5 5.5 0 0 1-11 0V4.5H3.75A.75.75 0 0 1 3 3.75ZM4 19.25a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H4.75a.75.75 0 0 1-.75-.75Z" fill="currentColor"/></svg>',
  
  'text-strikethrough': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.5 4A2.5 2.5 0 0 0 4 6.5a.5.5 0 0 1-1 0A3.5 3.5 0 0 1 6.5 3h7A3.5 3.5 0 0 1 17 6.5c0 1.41-.83 2.64-2.05 3.23l-.2.1H17a.5.5 0 0 1 0 1h-4.5v.67c0 .92.75 1.67 1.67 1.67h1.66c.92 0 1.67-.75 1.67-1.67a.5.5 0 0 1 1 0A2.67 2.67 0 0 1 15.83 14h-1.66A2.67 2.67 0 0 1 11.5 11.33v-.5H3a.5.5 0 0 1 0-1h5.5V9.17c0-.92-.75-1.67-1.67-1.67H5.17c-.92 0-1.67.75-1.67 1.67a.5.5 0 0 1-1 0A2.67 2.67 0 0 1 5.17 6.5h1.66c.92 0 1.67.75 1.67 1.67v1.66h2.5v-.5c0-.92.75-1.67 1.67-1.67h1.66c.92 0 1.67.75 1.67 1.67v.5H17a.5.5 0 0 1 0 1h-1.5v.67c0 .92-.75 1.67-1.67 1.67h-1.66c-.92 0-1.67-.75-1.67-1.67v-.5H8.5v.5c0 .92-.75 1.67-1.67 1.67H5.17c-.92 0-1.67-.75-1.67-1.67a.5.5 0 0 1 1 0c0 .37.3.67.67.67h1.66c.37 0 .67-.3.67-.67v-.5H3a.5.5 0 0 1 0-1h4.5V9.17c0-.37-.3-.67-.67-.67H5.17c-.37 0-.67.3-.67.67a.5.5 0 0 1-1 0c0-1.1.9-2 2-2h1.66c1.1 0 2 .9 2 2v1.66h2.5V9.17c0-.37.3-.67.67-.67h1.66c.37 0 .67.3.67.67v1.66H17a.5.5 0 0 1 0 1h-1.5v.67c0 .37-.3.67-.67.67h-1.66c-.37 0-.67-.3-.67-.67v-.5H8.5v.5c0 .37-.3.67-.67.67H6.17c-.37 0-.67-.3-.67-.67a.5.5 0 0 1 1 0c0 .18.15.33.33.33h1.34c.18 0 .33-.15.33-.33v-.5H3a.5.5 0 0 1 0-1h4.5V9.17c0-.18-.15-.33-.33-.33H5.83c-.18 0-.33.15-.33.33a.5.5 0 0 1-1 0c0-.73.6-1.33 1.33-1.33h1.34c.73 0 1.33.6 1.33 1.33v1.66h2.5V9.17c0-.18.15-.33.33-.33h1.34c.18 0 .33.15.33.33v1.66H17a.5.5 0 0 1 0 1h-1.5v.67c0 .18-.15.33-.33.33h-1.34c-.18 0-.33-.15-.33-.33v-.5H8.5v.5c0 .18-.15.33-.33.33H6.83c-.18 0-.33-.15-.33-.33a.5.5 0 0 1 1 0v.17h1.34v-.17H3a.5.5 0 0 1 0-1h4.5V9.17h2.5v1.66H17a.5.5 0 0 1 0 1h-7Z" fill="currentColor"/></svg>',
  
  'code': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.854 5.146a.5.5 0 0 0-.708.708L8.293 8l-2.147 2.146a.5.5 0 0 0 .708.708L9.207 8.5a.5.5 0 0 0 0-.708L6.854 5.146ZM11 12a.5.5 0 0 0 0 1h3a.5.5 0 0 0 0-1h-3Z" fill="currentColor"/></svg>',
  
  'image': '<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.75 3A3.75 3.75 0 0 0 3 6.75v14.5A3.75 3.75 0 0 0 6.75 25h14.5A3.75 3.75 0 0 0 25 21.25V6.75A3.75 3.75 0 0 0 21.25 3H6.75ZM4.5 6.75A2.25 2.25 0 0 1 6.75 4.5h14.5a2.25 2.25 0 0 1 2.25 2.25v14.5a2.25 2.25 0 0 1-2.25 2.25H6.75a2.25 2.25 0 0 1-2.25-2.25V6.75ZM9.5 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM11 8.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Zm-4.28 9.22a.75.75 0 0 1 1.06 0l3.5-3.5a.75.75 0 0 1 1.06 0l1.94 1.94 4.22-4.22a.75.75 0 0 1 1.06 0l3.5 3.5a.75.75 0 0 1-1.06 1.06L18.5 13.06l-4.22 4.22a.75.75 0 0 1-1.06 0L11.28 15.34l-2.94 2.94a.75.75 0 0 1-1.06-1.06Z" fill="currentColor"/></svg>',
  
  'arrow-undo': '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.854 5.146a.5.5 0 1 0-.708.708l2 2a.5.5 0 0 0 .708 0l2-2a.5.5 0 0 0-.708-.708L6 6.293V4.5A4.5 4.5 0 0 1 10.5 0h1a.5.5 0 0 1 0 1h-1A3.5 3.5 0 0 0 7 4.5v1.793l1.146-1.147a.5.5 0 0 1 .708.708l-2 2a.5.5 0 0 1-.708 0l-2-2Z" fill="currentColor"/></svg>',
  
  'arrow-redo': '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.146 5.146a.5.5 0 0 1 .708.708l-2 2a.5.5 0 0 1-.708 0l-2-2a.5.5 0 0 1 .708-.708L10 6.293V4.5A3.5 3.5 0 0 0 6.5 1h-1a.5.5 0 0 1 0-1h1A4.5 4.5 0 0 1 11 4.5v1.793l1.146-1.147Z" fill="currentColor"/></svg>',
  
  'line-horizontal': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 10a.5.5 0 0 1 .5-.5h13a.5.5 0 0 1 0 1h-13A.5.5 0 0 1 3 10Z" fill="currentColor"/></svg>',
  
  'video-clip': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 6.75A2.75 2.75 0 0 1 4.75 4h14.5A2.75 2.75 0 0 1 22 6.75v10.5A2.75 2.75 0 0 1 19.25 20H4.75A2.75 2.75 0 0 1 2 17.25V6.75ZM4.75 5.5c-.69 0-1.25.56-1.25 1.25v10.5c0 .69.56 1.25 1.25 1.25h14.5c.69 0 1.25-.56 1.25-1.25V6.75c0-.69-.56-1.25-1.25-1.25H4.75ZM10 8.5a.5.5 0 0 1 .8-.4l4 3a.5.5 0 0 1 0 .8l-4 3a.5.5 0 0 1-.8-.4v-6Z" fill="currentColor"/></svg>',
  
  'fullscreen-maximize': '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 3.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1H4v2.5a.5.5 0 0 1-1 0v-3ZM9.5 3a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-1 0V4h-2.5a.5.5 0 0 1-.5-.5ZM3 9.5a.5.5 0 0 1 .5.5v2.5H6a.5.5 0 0 1 0 1H3.5a.5.5 0 0 1-.5-.5v-3a.5.5 0 0 1 .5-.5ZM12.5 9a.5.5 0 0 1 .5.5v3a.5.5 0 0 1-.5.5H10a.5.5 0 0 1 0-1h2.5V9.5a.5.5 0 0 1 .5-.5Z" fill="currentColor"/></svg>',
  
  'resize-small': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6.5 4a.5.5 0 0 0-.5.5v3a.5.5 0 0 1-1 0v-3A1.5 1.5 0 0 1 6.5 3h3a.5.5 0 0 1 0 1h-3ZM11 4.5a.5.5 0 0 1 .5-.5h3A1.5 1.5 0 0 1 16 5.5v3a.5.5 0 0 1-1 0v-3a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 1-.5-.5ZM4.5 11a.5.5 0 0 1 .5.5v3a.5.5 0 0 0 .5.5h3a.5.5 0 0 1 0 1h-3A1.5 1.5 0 0 1 4 14.5v-3a.5.5 0 0 1 .5-.5ZM15.5 11a.5.5 0 0 1 .5.5v3a1.5 1.5 0 0 1-1.5 1.5h-3a.5.5 0 0 1 0-1h3a.5.5 0 0 0 .5-.5v-3a.5.5 0 0 1 .5-.5Z" fill="currentColor"/></svg>',
  
  'text-bullet-list': '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0ZM6 4.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5ZM2 8a1 1 0 1 1 2 0 1 1 0 0 1-2 0ZM6.5 7.5a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1h-7ZM2 12a1 1 0 1 1 2 0 1 1 0 0 1-2 0ZM6.5 11.5a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1h-7Z" fill="currentColor"/></svg>',
  
  'text-number-list': '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.5 2a.5.5 0 0 1 .5.5V5h.5a.5.5 0 0 1 0 1H2a.5.5 0 0 1-.5-.5v-3a.5.5 0 0 1 .5-.5ZM6 4.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5ZM2 7.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5H3v.5h.5a.5.5 0 0 1 0 1H2a.5.5 0 0 1 0-1h.5V9H2.5a.5.5 0 0 1-.5-.5v-1ZM6.5 7.5a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1h-7ZM2 11.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v.5a.5.5 0 0 1-.5.5h-.5v.5h1a.5.5 0 0 1 0 1H2a.5.5 0 0 1 0-1h.5v-.5H2.5a.5.5 0 0 1-.5-.5v-.5ZM6.5 11.5a.5.5 0 0 0 0 1h7a.5.5 0 0 0 0-1h-7Z" fill="currentColor"/></svg>',
  
  'task-list': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6ZM8.75 6.5a.75.75 0 0 1 .75-.75h10a.75.75 0 0 1 0 1.5h-10a.75.75 0 0 1-.75-.75ZM3 12a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-1ZM9.5 11.25a.75.75 0 0 0 0 1.5h10a.75.75 0 0 0 0-1.5h-10ZM3 18a1 1 0 0 1 1-1h1a1 1 0 0 1 1 1v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-1ZM9.5 17.25a.75.75 0 0 0 0 1.5h10a.75.75 0 0 0 0-1.5h-10Z" fill="currentColor"/></svg>',
  
  'text-align-left': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.25ZM3 10.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75ZM3.75 13.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75ZM3 18.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z" fill="currentColor"/></svg>',
  
  'text-align-center': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.25ZM6 10.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75a.75.75 0 0 1-.75-.75ZM3.75 13.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75ZM6 18.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75a.75.75 0 0 1-.75-.75Z" fill="currentColor"/></svg>',
  
  'text-align-right': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.25ZM9.75 9.5a.75.75 0 0 0 0 1.5h10.5a.75.75 0 0 0 0-1.5H9.75ZM3.75 13.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75ZM9.75 17.5a.75.75 0 0 0 0 1.5h10.5a.75.75 0 0 0 0-1.5H9.75Z" fill="currentColor"/></svg>',
  
  'text-align-justify': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 6.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.25ZM3.75 9.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75ZM3 14.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75ZM3.75 17.5a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5H3.75Z" fill="currentColor"/></svg>',
  
  'search': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 2.5a7.5 7.5 0 0 1 5.964 12.048l4.743 4.744a1 1 0 0 1-1.414 1.414l-4.744-4.743A7.5 7.5 0 1 1 10 2.5ZM10 4a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11Z" fill="currentColor"/></svg>',
  
  'add': '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.883 3.007 12 3a1 1 0 0 1 .993.883L13 4v7h7a1 1 0 0 1 .993.883L21 12a1 1 0 0 1-.883.993L20 13h-7v7a1 1 0 0 1-.883.993L12 21a1 1 0 0 1-.993-.883L11 20v-7H4a1 1 0 0 1-.993-.883L3 12a1 1 0 0 1 .883-.993L4 11h7V4a1 1 0 0 1 .883-.993Z" fill="currentColor"/></svg>',
  
  'document-edit': '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h4.5a.5.5 0 0 0 0-1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5v2.5A1.5 1.5 0 0 0 10.5 7H13v1.5a.5.5 0 0 0 1 0V6.5a1 1 0 0 0-.293-.707l-3.5-3.5A1 1 0 0 0 9.5 2H4Zm6 .5 2.5 2.5H10.5a.5.5 0 0 1-.5-.5V2.5Zm1.146 7.646a.5.5 0 0 1 .708 0l1.5 1.5a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.354.146H7a.5.5 0 0 1-.5-.5v-2a.5.5 0 0 1 .146-.354l4-4ZM8 13.293l3.5-3.5L12.793 11 9.293 14.5H8v-1.207Z" fill="currentColor"/></svg>',
  
  'star': '<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 4.25c.69 0 1.32.44 1.55 1.11l4.47 12.89 13.73.02c.72 0 1.37.46 1.6 1.14.23.68-.01 1.43-.59 1.86l-11.11 8.18 4.24 12.94c.22.68-.02 1.43-.6 1.86-.58.43-1.37.43-1.95 0L24 35.07 13.66 44.25c-.58.43-1.37.43-1.95 0-.58-.43-.82-1.18-.6-1.86l4.24-12.94L4.24 21.27c-.58-.43-.82-1.18-.59-1.86.23-.68.88-1.14 1.6-1.14l13.73-.02L23.45 5.36c.23-.67.86-1.11 1.55-1.11Z" fill="currentColor"/></svg>',
  
  'comment-note': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 2a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h1.5l2.4 3.2a.5.5 0 0 0 .8 0L12.5 13H14a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6Zm0 1h8a1 1 0 0 1 1 1v7a1 1 0 0 1-1 1h-2a.5.5 0 0 0-.4.2L10 14.33 8.4 12.2a.5.5 0 0 0-.4-.2H6a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z" fill="currentColor"/></svg>',
  
  'arrow-right': '<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.73 4.2a.75.75 0 0 1 1.06.04L14.8 10l-6.01 5.76a.75.75 0 1 1-1.04-1.08L12.2 10 7.75 5.32a.75.75 0 0 1-.02-1.12Z" fill="currentColor"/></svg>',
  
  // Antd Icons
  'link': '<svg width="1em" height="1em" viewBox="0 0 1024 1024" fill="currentColor"><path d="M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"/></svg>',
  
  'rollback': '<svg width="1em" height="1em" viewBox="0 0 1024 1024" fill="currentColor"><path d="M793 242H366v-74c0-6.7-7.7-10.4-12.9-6.3l-142 112a8 8 0 0 0 0 12.6l142 112c5.2 4.1 12.9.4 12.9-6.3v-74h415v470H175c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h618c35.3 0 64-28.7 64-64V306c0-35.3-28.7-64-64-64z"/></svg>',
  
  'like': '<svg width="1em" height="1em" viewBox="0 0 1024 1024" fill="currentColor"><path d="M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7 0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 0 0-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 0 0 471 99.9c-52 0-98 35-111.8 85.1l-85.9 311H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h601.3c9.2 0 18.2-1.8 26.8-5.4 22.6-9.4 41.6-26.7 53.2-48.9a112.34 112.34 0 0 0 8.1-106.2L885.9 533.7zM569.3 864.2H184V536h141.4l102.8-370.9c6.2-22.6 25.4-39.1 48.8-39.1 12.1 0 23.2 4.7 31.9 13.2 8.7 8.5 13.7 19.9 13.2 32.3L506 318.5h314.9c8.5 0 16.6 4.5 21.3 11.8 4.6 7.2 5.6 16.3 2.8 24.1L569.3 864.2z"/></svg>',
  
  'dislike': '<svg width="1em" height="1em" viewBox="0 0 1024 1024" fill="currentColor"><path d="M885.9 490.3c3.6-12 2.4-24.9-3.7-35.9l-175-253.9c-16.6-23.8-43.5-38.1-72.5-38.1H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h129.3l85.8 310.8C372.9 949 418.9 984 470.9 984c27.4 0 53.7-10.9 73.1-30.7 19.3-19.7 30.1-46.6 30.1-75.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3 40.4-23.5 65.5-66.1 65.5-111 0-28.2-9.3-55.5-26.1-77.7zM184 456V172h430.4c14.7 0 28.3 6.8 37.2 18.7L826.6 444c2.8 4.1 4.3 8.7 4.3 13.6 0 5.1-1.6 10.1-4.6 14.1s-7.2 7-12.1 8.5c-5 1.5-10.2 1.9-15.1.8L569.3 456H184zm-32 424c0-17.7 14.3-32 32-32h129.3l85.8 310.8C372.9 949 418.9 984 470.9 984c27.4 0 53.7-10.9 73.1-30.7 19.3-19.7 30.1-46.6 30.1-75.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3 40.4-23.5 65.5-66.1 65.5-111 0-28.2-9.3-55.5-26.1-77.7l-175-253.9c-16.6-23.8-43.5-38.1-72.5-38.1H184v364z"/></svg>',
  
  'comment': '<svg width="1em" height="1em" viewBox="0 0 1024 1024" fill="currentColor"><path d="M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z"/><path d="M894 345c-48.1-66-115.3-110.1-189-130v.1c-17.1-19-36.4-36.5-58-52.1-163.7-119-393.5-82.7-513 81-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4 5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6 17.7 0 32-14.3 32-32V753.8c88.1-119.6 90.4-284.9 1-408.8zM323 735l-12-5-99 31-1-104-8-9c-84.6-103.2-90.2-251.9-11-361 96.4-132.2 281.2-161.4 413-66 132.2 96.1 161.5 280.6 66 412-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10 1 104-99-31-12 5c-56.9 24.9-119.2 30.4-178.1 15.9 50.4-26.4 97.4-64 140.2-112.1 27.4-31 50.9-65.1 69.5-101.9 54.4-108 49-237.6-14.8-339.1 87.9 17.1 159.6 75.4 196.8 152.1 45.2 93.1 40.8 199.9-94.6 307.1z"/></svg>',
  
  // Ionicons4
  'ios-code': '<svg width="1em" height="1em" viewBox="0 0 512 512" fill="currentColor"><path d="M161.98 397.63L23.37 259.02 161.98 120.4l22.63 22.63L69.63 259.02l114.98 115.98-22.63 22.63zM350.02 397.63l-22.63-22.63L442.37 259.02 327.39 143.03l22.63-22.63L488.63 259.02 350.02 397.63z"/></svg>',
  
  'ios-notifications-outline': '<svg width="1em" height="1em" viewBox="0 0 512 512" fill="currentColor"><path d="M267.2 435.2c0 14.9-12.1 27-27 27s-27-12.1-27-27 12.1-27 27-27 27 12.1 27 27zM448 358.4V219.2c0-72.1-58.9-131-131-131s-131 58.9-131 131v139.2L144 400v32h224v-32l-42-41.6zM400 400H112v-16l32-32V219.2c0-89.6 72.6-162.2 162.2-162.2S468.4 129.6 468.4 219.2V352l32 32v16z"/></svg>',
  
  'ios-notifications-off': '<svg width="1em" height="1em" viewBox="0 0 512 512" fill="currentColor"><path d="M267.2 435.2c0 14.9-12.1 27-27 27s-27-12.1-27-27 12.1-27 27-27 27 12.1 27 27zM448 358.4V219.2c0-72.1-58.9-131-131-131s-131 58.9-131 131v139.2L144 400v32h224v-32l-42-41.6z"/></svg>',
  
  'ios-close': '<svg width="1em" height="1em" viewBox="0 0 512 512" fill="currentColor"><path d="M289.94 256l95-95A24 24 0 0 0 351 127l-95 95-95-95a24 24 0 0 0-34 34l95 95-95 95a24 24 0 1 0 34 34l95-95 95 95a24 24 0 0 0 34-34z"/></svg>',
  
  // Tabler Icons
  'blockquote': '<svg width="1em" height="1em" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 15h15"/><path d="M21 19H6"/><path d="M15 11h6"/><path d="M21 7H3"/><path d="M3 3v18"/></svg>',
} as const;

// 图标名称类型
export type IconName = keyof typeof iconSvgs;

// 获取图标SVG字符串
export function getIconSvg(name: IconName): string {
  return iconSvgs[name];
}

// 检查图标是否存在
export function hasIcon(name: string): name is IconName {
  return name in iconSvgs;
}

// 获取所有图标名称
export function getAllIconNames(): IconName[] {
  return Object.keys(iconSvgs) as IconName[];
}

// 创建图标元素（用于TS/JS中直接使用）
export function createIconElement(name: IconName, options?: {
  size?: string | number;
  color?: string;
  className?: string;
}): HTMLElement {
  const { size = '1em', color = 'currentColor', className = '' } = options || {};

  if (!hasIcon(name)) {
    console.warn(`Icon "${name}" not found`);
    const span = document.createElement('span');
    span.textContent = name;
    span.className = 'icon-fallback';
    return span;
  }

  const wrapper = document.createElement('span');
  wrapper.className = `icon-wrapper ${className}`.trim();

  let svg = getIconSvg(name);

  // 替换颜色
  if (color !== 'currentColor') {
    svg = svg.replace(/fill="currentColor"/g, `fill="${color}"`);
    svg = svg.replace(/stroke="currentColor"/g, `stroke="${color}"`);
  }

  // 设置尺寸
  const sizeValue = typeof size === 'number' ? `${size}px` : size;
  svg = svg.replace(/width="[^"]*"/g, `width="${sizeValue}"`);
  svg = svg.replace(/height="[^"]*"/g, `height="${sizeValue}"`);

  wrapper.innerHTML = svg;
  wrapper.style.display = 'inline-flex';
  wrapper.style.alignItems = 'center';
  wrapper.style.justifyContent = 'center';
  wrapper.style.width = sizeValue;
  wrapper.style.height = sizeValue;
  wrapper.style.color = color;

  return wrapper;
}

// 获取图标HTML字符串（用于innerHTML）
export function getIconHtml(name: IconName, options?: {
  size?: string | number;
  color?: string;
  className?: string;
}): string {
  const element = createIconElement(name, options);
  return element.outerHTML;
}
