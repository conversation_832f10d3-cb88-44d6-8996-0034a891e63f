/**
 * 图标系统测试文件
 * 用于验证各项功能是否正常工作
 */

import { 
  getIconSvg, 
  hasIcon, 
  getAllIconNames, 
  createIconElement, 
  getIconHtml,
  getIconCount 
} from './index';

import { 
  mapVIconToIconName,
  fluentIconMapping,
  antdIconMapping 
} from './mapping';

import { 
  searchIcons,
  getIconMetadata,
  getIconStats,
  getAllCategories,
  getIconsByCategory 
} from './utils';

import { 
  getConfig,
  updateConfig,
  resetConfig 
} from './config';

import { 
  getCachedIcon,
  setCachedIcon,
  clearIconCache,
  getIconCacheStats 
} from './cache';

// 测试结果接口
interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
}

// 测试套件
class IconSystemTest {
  private results: TestResult[] = [];
  
  // 运行所有测试
  runAllTests(): TestResult[] {
    console.log('🧪 开始运行图标系统测试...\n');
    
    this.testBasicFunctions();
    this.testMapping();
    this.testUtils();
    this.testConfig();
    this.testCache();
    this.testDOMOperations();
    
    this.printResults();
    return this.results;
  }
  
  // 添加测试结果
  private addResult(name: string, passed: boolean, error?: string) {
    this.results.push({ name, passed, error });
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${name}${error ? `: ${error}` : ''}`);
  }
  
  // 测试基础功能
  private testBasicFunctions() {
    console.log('📦 测试基础功能...');
    
    try {
      // 测试图标数量
      const count = getIconCount();
      this.addResult('获取图标数量', count > 0);
      
      // 测试获取所有图标名称
      const names = getAllIconNames();
      this.addResult('获取所有图标名称', names.length > 0);
      
      // 测试图标存在检查
      this.addResult('检查图标存在', hasIcon('search'));
      this.addResult('检查图标不存在', !hasIcon('non-existent-icon'));
      
      // 测试获取SVG
      const svg = getIconSvg('search');
      this.addResult('获取图标SVG', svg.includes('<svg'));
      
    } catch (error) {
      this.addResult('基础功能测试', false, String(error));
    }
  }
  
  // 测试映射功能
  private testMapping() {
    console.log('\n🔄 测试映射功能...');
    
    try {
      // 测试fluent映射
      const fluentIcon = mapVIconToIconName('fluent', 'TextBold20Filled');
      this.addResult('Fluent图标映射', fluentIcon === 'text-bold');
      
      // 测试antd映射
      const antdIcon = mapVIconToIconName('antd', 'LikeOutlined');
      this.addResult('Antd图标映射', antdIcon === 'like');
      
      // 测试不存在的映射
      const nonExistent = mapVIconToIconName('fluent', 'NonExistentIcon');
      this.addResult('不存在的图标映射', nonExistent === null);
      
    } catch (error) {
      this.addResult('映射功能测试', false, String(error));
    }
  }
  
  // 测试工具函数
  private testUtils() {
    console.log('\n🛠️ 测试工具函数...');
    
    try {
      // 测试搜索功能
      const searchResults = searchIcons('search');
      this.addResult('图标搜索', searchResults.length > 0);
      
      // 测试获取元数据
      const metadata = getIconMetadata('search');
      this.addResult('获取图标元数据', metadata.name === 'search');
      
      // 测试获取分类
      const categories = getAllCategories();
      this.addResult('获取所有分类', categories.length > 0);
      
      // 测试按分类获取图标
      const textIcons = getIconsByCategory('text');
      this.addResult('按分类获取图标', textIcons.length > 0);
      
      // 测试统计信息
      const stats = getIconStats();
      this.addResult('获取统计信息', stats.total > 0);
      
    } catch (error) {
      this.addResult('工具函数测试', false, String(error));
    }
  }
  
  // 测试配置功能
  private testConfig() {
    console.log('\n⚙️ 测试配置功能...');
    
    try {
      // 获取默认配置
      const defaultConfig = getConfig();
      this.addResult('获取默认配置', defaultConfig.defaultSize === '1em');
      
      // 更新配置
      updateConfig({ defaultSize: '20px' });
      const updatedConfig = getConfig();
      this.addResult('更新配置', updatedConfig.defaultSize === '20px');
      
      // 重置配置
      resetConfig();
      const resetConfig = getConfig();
      this.addResult('重置配置', resetConfig.defaultSize === '1em');
      
    } catch (error) {
      this.addResult('配置功能测试', false, String(error));
    }
  }
  
  // 测试缓存功能
  private testCache() {
    console.log('\n💾 测试缓存功能...');
    
    try {
      // 清空缓存
      clearIconCache();
      
      // 设置缓存
      setCachedIcon('test-icon', '<svg>test</svg>');
      
      // 获取缓存
      const cached = getCachedIcon('test-icon');
      this.addResult('缓存设置和获取', cached === '<svg>test</svg>');
      
      // 获取缓存统计
      const stats = getIconCacheStats();
      this.addResult('获取缓存统计', typeof stats.size === 'number');
      
    } catch (error) {
      this.addResult('缓存功能测试', false, String(error));
    }
  }
  
  // 测试DOM操作
  private testDOMOperations() {
    console.log('\n🌐 测试DOM操作...');
    
    try {
      // 测试创建图标元素
      const element = createIconElement('search', { size: '20px' });
      this.addResult('创建图标元素', element instanceof HTMLElement);
      
      // 测试获取HTML字符串
      const html = getIconHtml('add', { size: 16 });
      this.addResult('获取HTML字符串', html.includes('<span'));
      
    } catch (error) {
      this.addResult('DOM操作测试', false, String(error));
    }
  }
  
  // 打印测试结果
  private printResults() {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const failed = total - passed;
    
    console.log('\n📊 测试结果汇总:');
    console.log(`总计: ${total} 个测试`);
    console.log(`通过: ${passed} 个测试`);
    console.log(`失败: ${failed} 个测试`);
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => console.log(`  - ${r.name}: ${r.error}`));
    }
    
    const successRate = (passed / total * 100).toFixed(1);
    console.log(`\n成功率: ${successRate}%`);
    
    if (failed === 0) {
      console.log('🎉 所有测试都通过了！');
    }
  }
}

// 导出测试函数
export function runIconSystemTests(): TestResult[] {
  const tester = new IconSystemTest();
  return tester.runAllTests();
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location?.search?.includes('test=icons')) {
  runIconSystemTests();
}
