'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 48 48'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'Accessibility48Filled',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'g',
              {
                fill: 'none'
              },
              [
                (0, vue_1.createElementVNode)('path', {
                  d: 'M24 14.5A5.25 5.25 0 1 0 24 4a5.25 5.25 0 0 0 0 10.5zm-12.292-4.242a4.25 4.25 0 0 0-2.907 7.987l7.872 2.865a.5.5 0 0 1 .329.47v4.438c0 .353-.068.703-.2 1.03l-4.49 11.112a4.25 4.25 0 1 0 7.882 3.184l3.343-8.274a.5.5 0 0 1 .928 0l3.343 8.274a4.25 4.25 0 0 0 7.883-3.184l-4.488-11.104a2.747 2.747 0 0 1-.2-1.03v-4.447a.5.5 0 0 1 .33-.47l7.872-2.864a4.25 4.25 0 1 0-2.908-7.988l-3.812 1.388c-.82.298-1.422.896-1.805 1.544a7.747 7.747 0 0 1-6.677 3.81a7.747 7.747 0 0 1-6.677-3.81c-.384-.648-.986-1.246-1.806-1.544l-3.812-1.387z',
                  fill: 'currentColor'
                })
              ],
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
