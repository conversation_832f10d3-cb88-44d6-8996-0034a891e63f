// 图标系统工具函数
import { iconSvgs, getAllIconNames } from './index';
import { getAllMappings } from './mapping';
import type { 
  IconName, 
  IconLibrary, 
  IconSearchResult, 
  IconStats, 
  IconMetadata 
} from './types';

// 图标分类映射
const iconCategories: Record<string, string[]> = {
  'text': ['text-header-1', 'text-header-2', 'text-header-3', 'text-bold', 'text-italic', 'text-underline', 'text-strikethrough'],
  'align': ['text-align-left', 'text-align-center', 'text-align-right', 'text-align-justify'],
  'list': ['text-bullet-list', 'text-number-list', 'task-list'],
  'media': ['image', 'video-clip', 'code'],
  'navigation': ['arrow-undo', 'arrow-redo', 'arrow-right', 'rollback', 'link'],
  'action': ['search', 'add', 'document-edit', 'ios-close'],
  'social': ['star', 'like', 'dislike', 'comment', 'comment-note'],
  'layout': ['fullscreen-maximize', 'resize-small', 'line-horizontal'],
  'notification': ['ios-notifications-outline', 'ios-notifications-off'],
  'quote': ['blockquote'],
};

// 搜索图标
export function searchIcons(query: string): IconSearchResult[] {
  const lowerQuery = query.toLowerCase();
  const results: IconSearchResult[] = [];
  
  getAllIconNames().forEach(name => {
    const lowerName = name.toLowerCase();
    
    // 名称匹配
    if (lowerName.includes(lowerQuery)) {
      results.push({
        name,
        svg: iconSvgs[name],
        metadata: getIconMetadata(name),
      });
    }
  });
  
  // 按匹配度排序
  return results.sort((a, b) => {
    const aIndex = a.name.toLowerCase().indexOf(lowerQuery);
    const bIndex = b.name.toLowerCase().indexOf(lowerQuery);
    return aIndex - bIndex;
  });
}

// 获取图标元数据
export function getIconMetadata(name: IconName): IconMetadata {
  const category = Object.keys(iconCategories).find(cat => 
    iconCategories[cat].includes(name)
  ) || 'other';
  
  // 从映射中查找原始名称和库
  const mappings = getAllMappings();
  let library: IconLibrary = 'fluent';
  let originalName: string | undefined;
  
  for (const [lib, mapping] of Object.entries(mappings)) {
    const original = Object.keys(mapping).find(key => mapping[key] === name);
    if (original) {
      library = lib as IconLibrary;
      originalName = original;
      break;
    }
  }
  
  return {
    name,
    library,
    originalName,
    category,
    tags: generateTags(name),
    description: generateDescription(name),
  };
}

// 生成图标标签
function generateTags(name: IconName): string[] {
  const tags: string[] = [];
  const parts = name.split('-');
  
  parts.forEach(part => {
    if (part.length > 2) {
      tags.push(part);
    }
  });
  
  // 添加分类标签
  const category = Object.keys(iconCategories).find(cat => 
    iconCategories[cat].includes(name)
  );
  if (category) {
    tags.push(category);
  }
  
  return [...new Set(tags)];
}

// 生成图标描述
function generateDescription(name: IconName): string {
  const descriptions: Record<string, string> = {
    'search': '搜索图标',
    'add': '添加/新建图标',
    'text-bold': '文本加粗图标',
    'text-italic': '文本斜体图标',
    'text-underline': '文本下划线图标',
    'text-strikethrough': '文本删除线图标',
    'text-header-1': '一级标题图标',
    'text-header-2': '二级标题图标',
    'text-header-3': '三级标题图标',
    'arrow-undo': '撤销图标',
    'arrow-redo': '重做图标',
    'star': '星标/收藏图标',
    'like': '点赞图标',
    'dislike': '点踩图标',
    'comment': '评论图标',
    'comment-note': '评论备注图标',
    'image': '图片图标',
    'video-clip': '视频图标',
    'code': '代码图标',
    'ios-code': '行内代码图标',
    'blockquote': '引用图标',
    'text-align-left': '左对齐图标',
    'text-align-center': '居中对齐图标',
    'text-align-right': '右对齐图标',
    'text-align-justify': '两端对齐图标',
    'text-bullet-list': '无序列表图标',
    'text-number-list': '有序列表图标',
    'task-list': '任务列表图标',
    'fullscreen-maximize': '全屏图标',
    'resize-small': '退出全屏图标',
    'line-horizontal': '分割线图标',
    'document-edit': '编辑文档图标',
    'arrow-right': '右箭头图标',
    'link': '链接图标',
    'rollback': '返回图标',
    'ios-notifications-outline': '通知开启图标',
    'ios-notifications-off': '通知关闭图标',
    'ios-close': '关闭图标',
  };
  
  return descriptions[name] || `${name}图标`;
}

// 按分类获取图标
export function getIconsByCategory(category: string): IconName[] {
  return iconCategories[category] || [];
}

// 获取所有分类
export function getAllCategories(): string[] {
  return Object.keys(iconCategories);
}

// 获取图标统计信息
export function getIconStats(): IconStats {
  const allIcons = getAllIconNames();
  const mappings = getAllMappings();
  
  const byLibrary: Record<IconLibrary, number> = {
    fluent: 0,
    antd: 0,
    ionicons4: 0,
    tabler: 0,
  };
  
  const categories: Record<string, number> = {};
  
  // 统计各库图标数量
  Object.keys(mappings.fluent).forEach(() => byLibrary.fluent++);
  Object.keys(mappings.antd).forEach(() => byLibrary.antd++);
  Object.keys(mappings.ionicons4).forEach(() => byLibrary.ionicons4++);
  Object.keys(mappings.tabler).forEach(() => byLibrary.tabler++);
  
  // 统计各分类图标数量
  Object.entries(iconCategories).forEach(([category, icons]) => {
    categories[category] = icons.length;
  });
  
  return {
    total: allIcons.length,
    byLibrary,
    categories,
  };
}

// 验证图标名称
export function validateIconName(name: string): name is IconName {
  return name in iconSvgs;
}

// 获取随机图标
export function getRandomIcon(): IconName {
  const allIcons = getAllIconNames();
  const randomIndex = Math.floor(Math.random() * allIcons.length);
  return allIcons[randomIndex];
}

// 获取相似图标
export function getSimilarIcons(name: IconName, limit: number = 5): IconName[] {
  const metadata = getIconMetadata(name);
  const category = metadata.category;
  
  if (category && iconCategories[category]) {
    return iconCategories[category]
      .filter(iconName => iconName !== name)
      .slice(0, limit);
  }
  
  return [];
}

// 批量获取图标SVG
export function getBatchIconSvgs(names: IconName[]): Record<IconName, string> {
  const result: Record<IconName, string> = {};
  
  names.forEach(name => {
    if (validateIconName(name)) {
      result[name] = iconSvgs[name];
    }
  });
  
  return result;
}
