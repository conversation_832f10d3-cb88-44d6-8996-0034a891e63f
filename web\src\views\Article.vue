<template>
  <div class="article-layout">
    <!-- 左侧文章 -->
    <!-- 骨架屏 -->
    <ArticleSkeleton :show="articleDetail.articleLoading.value" />
    <div v-if="!articleDetail.articleLoading.value" class="article-info-container">
      <div class="article-header">
        <div class="article-header-content-wrapper">
          <div class="article-header-content">
            <h2>{{ articleDetail.article.value.title }}</h2>
            <div class="article-tag-container">
              <NTag
                class="article-tag"
                v-for="tag in articleDetail.article.value.tags"
                :key="tag"
                type="primary"
                >{{ tag }}</NTag
              >
            </div>
          </div>
        </div>
        <div class="flex-column-start">
          <NGradientText
            type="info"
            class="display-block time-clickable"
            @click="articleTime.toggleTimeFormat('publish')"
          >
            发布时间：{{
              articleDetail.article.value.showExactPublishTime
                ? articleDetail.article.value.exactPublishedAt
                : articleDetail.article.value.publishedAt
            }}
          </NGradientText>
          <NGradientText
            type="info"
            class="display-block time-clickable"
            @click="articleTime.toggleTimeFormat('modify')"
          >
            最近修改：{{
              articleDetail.article.value.showExactModifyTime
                ? articleDetail.article.value.exactLastModified
                : articleDetail.article.value.lastModified
            }}
          </NGradientText>
          <NGradientText type="info" class="display-block">
            拥有者：{{ articleDetail.article.value.publisher }} | 等级：{{
              articleDetail.article.value.operationLevel
            }}
            | ip:
            {{ articleDetail.article.value.ipLocation }}
          </NGradientText>
        </div>
        <div class="action-buttons-container">
          <div class="edit-button-container">
            <Icon name="document-edit" />
            <Icon name="rollback" />
          </div>
          <!-- 互动按钮 -->
          <div class="interaction-container">
            <Icon name="like" />
            {{ articleDetail.article.value.likeCount }}
            <Icon name="dislike" />
            {{ articleDetail.article.value.dislikeCount }}
            <Icon name="star" />
            {{ articleDetail.article.value.favoriteCount }}
          </div>
          <div class="comment-count-container">
            <NIcon size="20"><CommentOutlined /></NIcon
            >{{ articleDetail.article.value.commentCount }}
          </div>
        </div>
      </div>
      <div class="article-content">
        <NScrollbar>
          <div style="padding-right: 1rem">
            <TiptapEditor
              v-model="articleDetail.article.value.contentObj"
              :editable="false"
              :file-bucket="ARTICLE"
              :all-extensions="true"
              :character-limit="ARTICLE_CHARACTER_LIMIT"
              :use-thumbnail="true"
            />
          </div>
        </NScrollbar>
      </div>
    </div>
    <!-- 编辑文章弹框 -->
    <ArticleModal ref="articleModalRef" @success="handleArticleSubmitSuccess" />
    <!-- 右侧评论 -->
    <CommentInfo
      ref="commentInfoRef"
      :articleId="articleDetail.getArticleId"
      @quick-reply-end="articleDetail.loadArticleDetailCount"
      @send-end="articleDetail.loadArticleDetailCount"
    />
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';



import { NIcon, NTag, NGradientText, NScrollbar } from 'naive-ui'
import { ref } from 'vue'

// 导入组件
import ArticleModal from '@/components/ArticleModal.vue'
import ArticleSkeleton from '@/components/ArticleSkeleton.vue'
import CommentInfo from '@/components/CommentInfo.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'

// 导入 composables
import { useArticleDetail } from '@/composables/article/useArticleDetail'
import { useArticleInteraction } from '@/composables/article/useArticleInteraction'
import { useArticleTime } from '@/composables/article/useArticleTime'

// 导入常量
import { ARTICLE } from '@/constants/bucket.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap.constants'

// 使用 composables
const articleDetail = useArticleDetail()
const articleInteraction = useArticleInteraction(articleDetail.article)
const articleTime = useArticleTime(articleDetail.article)

// 组件引用
const articleModalRef = ref()
const commentInfoRef = ref()

// 初始化
articleDetail.initialize()

// 打开编辑文章弹框
const openEditArticleDialog = () => {
  articleModalRef.value.openEditArticleDialog(articleDetail.article.value)
}

// 处理文章提交成功的回调函数
const handleArticleSubmitSuccess = () => {
  articleDetail.loadArticleDetail()
  commentInfoRef.value.loadCurrentCommentList()
}
</script>

<style scoped lang="scss">
.article-layout {
  display: flex;
  height: 100vh;
  height: 100dvh;
  width: 100vw;
  width: 100dvw;
  background-color: var(--gray-3);
  overflow-x: hidden;

  :deep(.article-info-container) {
    flex: 0 0 65vw;
    flex: 0 0 65dvw;
    width: 65%;
    padding: 1.25rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100vh;
    height: 100dvh;

    .article-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      position: relative;
      margin: 1.25rem 0;

      .article-header-content-wrapper {
        margin-top: 3rem;

        .article-header-content {
          display: flex;
          flex-direction: column;
          text-align: center;

          .article-tag-container {
            display: flex;
            gap: 0.5rem;
            justify-content: center;

            .article-tag {
              margin-left: 0;
            }
          }
        }
      }
    }

    .time-clickable {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        opacity: 0.8;
        text-decoration: underline;
      }
    }

    .action-buttons-container {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      position: absolute;
      top: -5%;
      right: 1%;

      .edit-button-container {
        margin-bottom: 0.5rem;
        display: flex;
        gap: 0.25rem;
      }

      .interaction-container {
        display: flex;
        font-size: 0.8rem;
        gap: 0.4rem;
        align-items: center;
      }

      .comment-count-container {
        margin-top: 0.5rem;
        margin-right: 0.25rem;
        font-size: 0.8rem;
        display: flex;
      }
    }

    .article-content {
      padding: 1rem 0 1rem 1rem;
      border-radius: 0.5rem;
      width: 90%;
      overflow-y: auto;
      background-color: var(--white-1);

      // 确保图片在文章内容中比例正确
      :deep(.image-wrapper),
      :deep(img) {
        max-width: 100%;
        height: auto;
        object-fit: contain;
      }
    }
  }
}

@media (width <= 55rem) {
  .article-layout {
    flex-direction: column;
  }

  .article-layout .article-info-container,
  .article-layout .comment-info-container {
    flex: 0 0 100vw;
    flex: 0 0 100dvw;
    width: 100%;
  }

  .article-layout .article-info-container {
    .article-content {
      width: 95%;
      padding: 1rem 0 1rem 1rem;

      // 小屏幕上进一步优化图片显示
      :deep(.ProseMirror) {
        p > .image-wrapper,
        p > img {
          max-width: 100% !important;
          min-width: unset !important;
          width: auto !important;
          height: auto !important;
        }
      }
    }

    .article-header {
      margin: 0.75rem 0;

      h2 {
        font-size: 1.4rem;
        word-break: break-word;
      }
    }
  }
}
</style>
