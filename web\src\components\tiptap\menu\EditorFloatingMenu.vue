<template>
  <FloatingMenu :tippy-options="tippyOptions" :editor="editor as Editor" :should-show="shouldShow">
    <div class="editor-floating-menu">
      <TiptapBtn
        :icon="TextHeader320Filled"
        :show="extensionsSet.has('heading')"
        :trigger="() => editor?.chain().focus().toggleHeading({ level: 3 }).run()"
        :is-active="editor?.isActive('heading', { level: 3 })"
        tooltip="标题3"
      />
      <TiptapBtn
        :icon="LinkOutlined"
        :show="extensionsSet.has('link')"
        :trigger="() => showModal('插入链接', insertLink)"
        :is-active="editor?.isActive('link')"
        tooltip="链接"
      />
      <TiptapBtn
        :icon="Image28Regular"
        :show="extensionsSet.has('image')"
        :trigger="() => $emit('image-upload')"
        tooltip="图片"
      />
      <ColorPicker
        :show="extensionsSet.has('color')"
        type="floating-menu"
        :editor="editor"
        colorType="color"
      />
      <ColorPicker
        :show="extensionsSet.has('backgroundColor')"
        type="floating-menu"
        :editor="editor"
        colorType="backgroundColor"
      />

      <!-- 添加文本对齐按钮 -->
      <TiptapBtn
        :icon="TextAlignLeft24Filled"
        :show="extensionsSet.has('align')"
        :trigger="() => editor?.chain().focus().setTextAlign('left').run()"
        :is-active="editor?.isActive({ textAlign: 'left' })"
        tooltip="左对齐"
      />
      <TiptapBtn
        :icon="TextAlignCenter24Filled"
        :show="extensionsSet.has('align')"
        :trigger="() => editor?.chain().focus().setTextAlign('center').run()"
        :is-active="editor?.isActive({ textAlign: 'center' })"
        tooltip="居中对齐"
      />
      <TiptapBtn
        :icon="TextAlignRight24Filled"
        :show="extensionsSet.has('align')"
        :trigger="() => editor?.chain().focus().setTextAlign('right').run()"
        :is-active="editor?.isActive({ textAlign: 'right' })"
        tooltip="右对齐"
      />
      <TiptapBtn
        :icon="TextAlignJustify24Filled"
        :show="extensionsSet.has('align')"
        :trigger="() => editor?.chain().focus().setTextAlign('justify').run()"
        :is-active="editor?.isActive({ textAlign: 'justify' })"
        tooltip="两端对齐"
      />
    </div>
  </FloatingMenu>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';

import { FloatingMenu, Editor } from '@tiptap/vue-3'



import ColorPicker from '@/components/tiptap/extensions/color/ColorPicker.vue'
import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'

const props = defineProps({
  editor: {
    type: Object,
    required: true,
  },
  extensionsSet: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['image-upload', 'show-modal'])

// Floating Menu 位置配置
// 可选的位置值：
// - 'top': 在行上方居中显示
// - 'bottom': 在行下方居中显示
// - 'left': 在行左侧居中显示
// - 'right': 在行右侧居中显示
// - 'top-start': 在行上方左对齐显示
// - 'top-end': 在行上方右对齐显示
// - 'bottom-start': 在行下方左对齐显示
// - 'bottom-end': 在行下方右对齐显示
// - 'left-start': 在行左侧上对齐显示
// - 'left-end': 在行左侧下对齐显示
// - 'right-start': 在行右侧上对齐显示
// - 'right-end': 在行右侧下对齐显示

const tippyOptions = {
  duration: 100,
  appendTo: document.body,
  placement: 'right' as const, // 当前设置为在行上方显示
}

// 不再需要本地modal对象，将使用TipTapEditor中的modal
const showModal = (title: string, trigger: () => void, onlyInputValue = false) => {
  emit('show-modal', { title, trigger, onlyInputValue })
}

// 修改insertLink函数，使用TipTapEditor中传递的modal值
const insertLink = () => {
  // 这里不直接使用本地modal对象，而是在TipTapEditor.vue中的handlePositiveClick中
  // 通过trigger函数调用时，会使用TipTapEditor中的modal值
  props.editor
    ?.chain()
    .focus()
    .setLink({ href: '' }) // 先创建一个空链接，会在TipTapEditor中被正确设置
    .run()
}

interface FloatingMenuProps {
  editor: {
    isEditable: boolean
  }
  state: {
    selection: {
      $anchor: {
        pos: number
        parent: {
          type: { name: string }
          content: { size: number }
        }
      }
    }
    doc: {
      content: { size: number }
      resolve: (pos: number) => {
        parent: {
          type: { name: string }
        }
      }
    }
  }
}

const shouldShow = (props: FloatingMenuProps) => {
  const { editor, state } = props
  const { selection } = state
  const { $anchor } = selection // 移除未使用的empty变量

  const isParagraph = $anchor.parent.type.name === 'paragraph'
  const isEmpty = $anchor.parent.content.size === 0

  // 检查是否在代码块内部或紧邻代码块
  const isInCodeBlock = $anchor.parent.type.name === 'codeBlock'

  // 检查前一个或后一个节点是否是代码块
  let isNearCodeBlock = false
  const pos = $anchor.pos
  const doc = state.doc

  // 检查前一个节点
  if (pos > 0) {
    const beforePos = doc.resolve(pos - 1)
    if (beforePos.parent.type.name === 'codeBlock') {
      isNearCodeBlock = true
    }
  }

  // 检查后一个节点
  if (pos < doc.content.size) {
    const afterPos = doc.resolve(pos + 1)
    if (afterPos.parent.type.name === 'codeBlock') {
      isNearCodeBlock = true
    }
  }

  return isParagraph && isEmpty && editor.isEditable && !isInCodeBlock && !isNearCodeBlock
}
</script>
