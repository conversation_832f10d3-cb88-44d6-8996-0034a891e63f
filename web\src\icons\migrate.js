#!/usr/bin/env node

/**
 * vicons迁移脚本
 * 自动将项目中的vicons使用替换为新的图标系统
 */

const fs = require('fs');
const path = require('path');

// 图标库映射
const iconMappings = {
  fluent: {
    'TextHeader120Filled': 'text-header-1',
    'TextHeader220Filled': 'text-header-2',
    'TextHeader320Filled': 'text-header-3',
    'TextBold20Filled': 'text-bold',
    'TextUnderline24Filled': 'text-underline',
    'TextStrikethrough20Filled': 'text-strikethrough',
    'Code20Filled': 'code',
    'Image28Regular': 'image',
    'ArrowUndo16Filled': 'arrow-undo',
    'ArrowRedo16Filled': 'arrow-redo',
    'LineHorizontal120Filled': 'line-horizontal',
    'VideoClip24Regular': 'video-clip',
    'FullScreenMaximize16Filled': 'fullscreen-maximize',
    'ResizeSmall20Filled': 'resize-small',
    'TextBulletListLtr16Filled': 'text-bullet-list',
    'TextNumberListLtr16Filled': 'text-number-list',
    'TaskListLtr24Filled': 'task-list',
    'TextAlignRight24Filled': 'text-align-right',
    'TextAlignLeft24Filled': 'text-align-left',
    'TextAlignCenter24Filled': 'text-align-center',
    'TextAlignJustify24Filled': 'text-align-justify',
    'Search24Regular': 'search',
    'Add24Regular': 'add',
    'DocumentEdit16Regular': 'document-edit',
    'Star48Regular': 'star',
    'CommentNote20Regular': 'comment-note',
    'ArrowRight20Filled': 'arrow-right',
  },
  antd: {
    'LinkOutlined': 'link',
    'RollbackOutlined': 'rollback',
    'LikeOutlined': 'like',
    'DislikeOutlined': 'dislike',
    'CommentOutlined': 'comment',
  },
  ionicons4: {
    'IosCode': 'ios-code',
    'IosNotificationsOutline': 'ios-notifications-outline',
    'IosNotificationsOff': 'ios-notifications-off',
    'IosClose': 'ios-close',
  },
  tabler: {
    'Italic': 'text-italic',
    'Blockquote': 'blockquote',
  }
};

// 获取图标库名称
function getLibraryFromImport(importPath) {
  if (importPath.includes('@vicons/fluent')) return 'fluent';
  if (importPath.includes('@vicons/antd')) return 'antd';
  if (importPath.includes('@vicons/ionicons4')) return 'ionicons4';
  if (importPath.includes('@vicons/tabler')) return 'tabler';
  return null;
}

// 处理单个文件
function processFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  let newContent = content;
  let hasChanges = false;
  
  // 收集导入的图标
  const importedIcons = new Map(); // iconName -> library
  
  // 匹配import语句
  const importRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"](@vicons\/[^'"]+)['"]/g;
  let importMatch;
  
  while ((importMatch = importRegex.exec(content)) !== null) {
    const iconNames = importMatch[1].split(',').map(name => name.trim());
    const importPath = importMatch[2];
    const library = getLibraryFromImport(importPath);
    
    if (library) {
      iconNames.forEach(iconName => {
        // 处理别名导入 (as ItalicIcon)
        const cleanName = iconName.replace(/\s+as\s+\w+/g, '').trim();
        importedIcons.set(cleanName, library);
      });
    }
  }
  
  // 如果有vicons导入，添加新的图标组件导入
  if (importedIcons.size > 0) {
    // 移除原有的vicons导入
    newContent = newContent.replace(importRegex, '');
    
    // 添加新的导入
    const scriptMatch = newContent.match(/<script[^>]*>/);
    if (scriptMatch) {
      const insertPos = scriptMatch.index + scriptMatch[0].length;
      const importStatement = "\nimport Icon from '@/icons/Icon.vue';\n";
      newContent = newContent.slice(0, insertPos) + importStatement + newContent.slice(insertPos);
      hasChanges = true;
    }
  }
  
  // 替换NIcon + vicons组合
  importedIcons.forEach((library, iconName) => {
    const mapping = iconMappings[library];
    if (mapping && mapping[iconName]) {
      const newIconName = mapping[iconName];
      
      // 匹配 <NIcon><IconName /></NIcon> 模式
      const nIconRegex = new RegExp(`<NIcon[^>]*>\\s*<${iconName}[^>]*/>\\s*</NIcon>`, 'g');
      const replacement = `<Icon name="${newIconName}" />`;
      
      if (nIconRegex.test(newContent)) {
        newContent = newContent.replace(nIconRegex, replacement);
        hasChanges = true;
      }
      
      // 匹配 <NIcon><IconName></IconName></NIcon> 模式
      const nIconRegex2 = new RegExp(`<NIcon[^>]*>\\s*<${iconName}[^>]*></${iconName}>\\s*</NIcon>`, 'g');
      if (nIconRegex2.test(newContent)) {
        newContent = newContent.replace(nIconRegex2, replacement);
        hasChanges = true;
      }
    }
  });
  
  // 如果有变更，写回文件
  if (hasChanges) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✅ 已更新: ${filePath}`);
    return true;
  }
  
  return false;
}

// 递归处理目录
function processDirectory(dirPath) {
  const files = fs.readdirSync(dirPath);
  let totalUpdated = 0;
  
  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      // 跳过node_modules和dist目录
      if (file !== 'node_modules' && file !== 'dist' && file !== '.git') {
        totalUpdated += processDirectory(fullPath);
      }
    } else if (file.endsWith('.vue') || file.endsWith('.ts') || file.endsWith('.js')) {
      if (processFile(fullPath)) {
        totalUpdated++;
      }
    }
  });
  
  return totalUpdated;
}

// 主函数
function main() {
  const srcPath = path.join(__dirname, '../');
  
  console.log('🚀 开始迁移vicons到新图标系统...');
  console.log(`📁 扫描目录: ${srcPath}`);
  
  const updatedCount = processDirectory(srcPath);
  
  console.log(`\n✨ 迁移完成！`);
  console.log(`📊 共更新了 ${updatedCount} 个文件`);
  
  if (updatedCount > 0) {
    console.log('\n📝 后续步骤:');
    console.log('1. 检查更新的文件，确保替换正确');
    console.log('2. 测试功能是否正常');
    console.log('3. 可以移除@vicons相关依赖');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { processFile, processDirectory };
