<template>
  <div class="notification-btn" style="cursor: pointer" ref="buttonRef">
    <NBadge
      :max="99"
      :value="unreadCount"
      :show-zero="false"
      :show="notificationReceiveType !== NotificationReceiveType.CLOSE"
    >
      <NIcon size="24">
        <IosNotificationsOutline v-if="notificationReceiveType !== NotificationReceiveType.CLOSE" />
        <IosNotificationsOff v-else />
      </NIcon>
    </NBadge>
  </div>
</template>

<script setup lang="ts">
import Icon from '@/icons/Icon.vue';


import { NBadge, NIcon } from 'naive-ui'
import { ref } from 'vue'

import { NotificationReceiveType } from '@/constants/notification_receive_type.constants'

defineProps({
  unreadCount: {
    type: Number,
    default: 0,
  },
  notificationReceiveType: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits(['click', 'long-press'])

const buttonRef = ref<HTMLElement | null>(null)
</script>

<style scoped>
.notification-btn {
  position: relative;
  display: inline-block;
}
</style>
