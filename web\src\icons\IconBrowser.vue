<template>
  <div class="icon-browser">
    <div class="browser-header">
      <h2>图标浏览器</h2>
      <div class="search-bar">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索图标..."
          class="search-input"
        />
        <Icon name="search" size="16" class="search-icon" />
      </div>
    </div>
    
    <div class="browser-filters">
      <div class="filter-group">
        <label>分类:</label>
        <select v-model="selectedCategory" class="category-select">
          <option value="">全部</option>
          <option v-for="category in categories" :key="category" :value="category">
            {{ categoryLabels[category] || category }}
          </option>
        </select>
      </div>
      
      <div class="filter-group">
        <label>图标库:</label>
        <select v-model="selectedLibrary" class="library-select">
          <option value="">全部</option>
          <option value="fluent">Fluent</option>
          <option value="antd">Antd</option>
          <option value="ionicons4">Ionicons4</option>
          <option value="tabler">Tabler</option>
        </select>
      </div>
      
      <div class="stats">
        共 {{ filteredIcons.length }} 个图标
      </div>
    </div>
    
    <div class="icon-grid">
      <div
        v-for="iconResult in filteredIcons"
        :key="iconResult.name"
        class="icon-card"
        :class="{ active: selectedIcon === iconResult.name }"
        @click="selectIcon(iconResult.name)"
      >
        <div class="icon-preview">
          <Icon :name="iconResult.name" size="24" />
        </div>
        <div class="icon-info">
          <div class="icon-name">{{ iconResult.name }}</div>
          <div class="icon-meta">
            <span class="icon-library">{{ iconResult.metadata?.library }}</span>
            <span class="icon-category">{{ iconResult.metadata?.category }}</span>
          </div>
        </div>
        <div class="icon-actions">
          <button @click.stop="copyIconName(iconResult.name)" class="copy-btn">
            复制名称
          </button>
          <button @click.stop="copyIconSvg(iconResult.name)" class="copy-btn">
            复制SVG
          </button>
        </div>
      </div>
    </div>
    
    <!-- 选中图标详情 -->
    <div v-if="selectedIcon" class="icon-detail">
      <h3>图标详情</h3>
      <div class="detail-content">
        <div class="detail-preview">
          <Icon :name="selectedIcon" size="48" />
        </div>
        <div class="detail-info">
          <div class="detail-row">
            <label>名称:</label>
            <code>{{ selectedIcon }}</code>
          </div>
          <div class="detail-row">
            <label>库:</label>
            <span>{{ selectedIconMetadata?.library }}</span>
          </div>
          <div class="detail-row">
            <label>原始名称:</label>
            <code>{{ selectedIconMetadata?.originalName || '无' }}</code>
          </div>
          <div class="detail-row">
            <label>分类:</label>
            <span>{{ selectedIconMetadata?.category }}</span>
          </div>
          <div class="detail-row">
            <label>标签:</label>
            <span>{{ selectedIconMetadata?.tags?.join(', ') }}</span>
          </div>
        </div>
      </div>
      
      <div class="usage-examples">
        <h4>使用示例</h4>
        <div class="example-code">
          <pre><code>&lt;Icon name="{{ selectedIcon }}" /&gt;</code></pre>
          <button @click="copyCode(`<Icon name=\"${selectedIcon}\" />`)" class="copy-btn">
            复制
          </button>
        </div>
        <div class="example-code">
          <pre><code>import { getIconSvg } from '@/icons';
const svg = getIconSvg('{{ selectedIcon }}');</code></pre>
          <button @click="copyCode(`import { getIconSvg } from '@/icons';\nconst svg = getIconSvg('${selectedIcon}');`)" class="copy-btn">
            复制
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import Icon from './Icon.vue';
import { 
  searchIcons, 
  getAllCategories, 
  getIconMetadata, 
  getIconsByCategory,
  getAllIconNames 
} from './utils';
import { getIconSvg } from './index';
import type { IconName, IconSearchResult } from './types';

const searchQuery = ref('');
const selectedCategory = ref('');
const selectedLibrary = ref('');
const selectedIcon = ref<IconName | null>(null);

const categories = ref<string[]>([]);
const allIcons = ref<IconSearchResult[]>([]);

const categoryLabels: Record<string, string> = {
  'text': '文本格式',
  'align': '对齐方式',
  'list': '列表',
  'media': '媒体',
  'navigation': '导航',
  'action': '操作',
  'social': '社交',
  'layout': '布局',
  'notification': '通知',
  'quote': '引用',
};

// 计算过滤后的图标
const filteredIcons = computed(() => {
  let results = allIcons.value;
  
  // 搜索过滤
  if (searchQuery.value.trim()) {
    results = searchIcons(searchQuery.value);
  }
  
  // 分类过滤
  if (selectedCategory.value) {
    const categoryIcons = getIconsByCategory(selectedCategory.value);
    results = results.filter(icon => categoryIcons.includes(icon.name));
  }
  
  // 图标库过滤
  if (selectedLibrary.value) {
    results = results.filter(icon => 
      icon.metadata?.library === selectedLibrary.value
    );
  }
  
  return results;
});

// 选中图标的元数据
const selectedIconMetadata = computed(() => {
  return selectedIcon.value ? getIconMetadata(selectedIcon.value) : null;
});

// 选择图标
const selectIcon = (iconName: IconName) => {
  selectedIcon.value = iconName;
};

// 复制图标名称
const copyIconName = async (iconName: IconName) => {
  try {
    await navigator.clipboard.writeText(iconName);
    console.log('图标名称已复制');
  } catch (err) {
    console.error('复制失败:', err);
  }
};

// 复制图标SVG
const copyIconSvg = async (iconName: IconName) => {
  try {
    const svg = getIconSvg(iconName);
    await navigator.clipboard.writeText(svg);
    console.log('SVG已复制');
  } catch (err) {
    console.error('复制失败:', err);
  }
};

// 复制代码
const copyCode = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code);
    console.log('代码已复制');
  } catch (err) {
    console.error('复制失败:', err);
  }
};

// 初始化
onMounted(() => {
  categories.value = getAllCategories();
  
  // 加载所有图标
  const iconNames = getAllIconNames();
  allIcons.value = iconNames.map(name => ({
    name,
    svg: getIconSvg(name),
    metadata: getIconMetadata(name),
  }));
});
</script>

<style scoped>
.icon-browser {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.browser-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 8px 32px 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
}

.search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.browser-filters {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-weight: 500;
  color: #333;
}

.category-select,
.library-select {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.stats {
  margin-left: auto;
  color: #666;
  font-size: 14px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 40px;
}

.icon-card {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.icon-card.active {
  border-color: #1890ff;
  background: #f0f8ff;
}

.icon-preview {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.icon-info {
  text-align: center;
  margin-bottom: 12px;
}

.icon-name {
  font-weight: 500;
  margin-bottom: 4px;
  font-size: 14px;
}

.icon-meta {
  display: flex;
  justify-content: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.icon-library,
.icon-category {
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 3px;
}

.icon-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.copy-btn {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.icon-detail {
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fafafa;
}

.detail-content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
}

.detail-info {
  flex: 1;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.detail-row label {
  width: 80px;
  font-weight: 500;
  color: #333;
}

.detail-row code {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.usage-examples h4 {
  margin-bottom: 12px;
  color: #333;
}

.example-code {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.example-code pre {
  flex: 1;
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #333;
}
</style>
