<template>
  <Icon 
    v-if="mappedIconName"
    :name="mappedIconName"
    :size="size"
    :color="color"
    :clickable="clickable"
    @click="$emit('click', $event)"
  />
  <span v-else class="icon-fallback">
    {{ iconName }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import Icon from './Icon.vue';
import { mapVIconToIconName } from './mapping';

interface Props {
  library: 'fluent' | 'antd' | 'ionicons4' | 'tabler';
  iconName: string;
  size?: string | number;
  color?: string;
  clickable?: boolean;
}

interface Emits {
  (e: 'click', event: MouseEvent): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: '1em',
  color: 'currentColor',
  clickable: false,
});

defineEmits<Emits>();

// 映射到新的图标名称
const mappedIconName = computed(() => {
  return mapVIconToIconName(props.library, props.iconName);
});
</script>

<style scoped>
.icon-fallback {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8em;
  color: #999;
  border: 1px dashed #ccc;
  padding: 2px 4px;
  border-radius: 2px;
}
</style>
