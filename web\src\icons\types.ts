// 图标系统类型定义

import type { iconSvgs } from './index';

// 图标名称类型
export type IconName = keyof typeof iconSvgs;

// 图标库类型
export type IconLibrary = 'fluent' | 'antd' | 'ionicons4' | 'tabler';

// 图标选项接口
export interface IconOptions {
  size?: string | number;
  color?: string;
  className?: string;
}

// Vue组件Props接口
export interface IconProps {
  name: IconName;
  size?: string | number;
  color?: string;
  clickable?: boolean;
}

// VIcon组件Props接口
export interface VIconProps {
  library: IconLibrary;
  iconName: string;
  size?: string | number;
  color?: string;
  clickable?: boolean;
}

// 图标映射接口
export interface IconMapping {
  [key: string]: IconName;
}

// 所有映射接口
export interface AllMappings {
  fluent: IconMapping;
  antd: IconMapping;
  ionicons4: IconMapping;
  tabler: IconMapping;
}

// 图标元数据接口
export interface IconMetadata {
  name: IconName;
  library: IconLibrary;
  originalName?: string;
  category?: string;
  tags?: string[];
  description?: string;
}

// 图标搜索结果接口
export interface IconSearchResult {
  name: IconName;
  svg: string;
  metadata?: IconMetadata;
}

// 图标统计接口
export interface IconStats {
  total: number;
  byLibrary: Record<IconLibrary, number>;
  categories: Record<string, number>;
}

// 导出所有类型
export type {
  IconName,
  IconLibrary,
  IconOptions,
  IconProps,
  VIconProps,
  IconMapping,
  AllMappings,
  IconMetadata,
  IconSearchResult,
  IconStats,
};
