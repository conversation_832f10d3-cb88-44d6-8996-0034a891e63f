# 图标系统

统一的图标系统，支持Vue组件使用和TS/JS中的字符串使用，替代原有的@vicons依赖。

## 特性

- 🎯 **统一管理**: 所有图标SVG集中管理
- 🔧 **类型安全**: 完整的TypeScript类型支持
- 🎨 **灵活定制**: 支持自定义尺寸、颜色
- 📦 **轻量级**: 无外部依赖，按需加载
- 🔄 **兼容性**: 提供vicons迁移映射

## 使用方式

### 1. Vue组件中使用

```vue
<template>
  <!-- 基础使用 -->
  <Icon name="search" />
  
  <!-- 自定义尺寸和颜色 -->
  <Icon name="add" size="24" color="#1890ff" />
  
  <!-- 可点击图标 -->
  <Icon name="close" clickable @click="handleClose" />
</template>

<script setup>
import Icon from '@/icons/Icon.vue';

const handleClose = () => {
  console.log('关闭');
};
</script>
```

### 2. 兼容vicons的使用方式

```vue
<template>
  <!-- 替代原有的 NIcon + vicons 组合 -->
  <VIcon library="fluent" iconName="TextBold20Filled" />
  <VIcon library="antd" iconName="LikeOutlined" size="20" />
</template>

<script setup>
import VIcon from '@/icons/VIcon.vue';
</script>
```

### 3. TS/JS中直接使用

```typescript
import { createIconElement, getIconHtml, getIconSvg } from '@/icons';

// 创建图标DOM元素
const iconElement = createIconElement('search', {
  size: '20px',
  color: '#1890ff',
  className: 'my-icon'
});
document.body.appendChild(iconElement);

// 获取图标HTML字符串
const iconHtml = getIconHtml('add', { size: 16 });
element.innerHTML = iconHtml;

// 获取原始SVG字符串
const svgString = getIconSvg('close');
```

## 可用图标

### Fluent Icons
- `text-header-1`, `text-header-2`, `text-header-3` - 标题
- `text-bold`, `text-italic`, `text-underline`, `text-strikethrough` - 文本格式
- `code`, `image`, `video-clip` - 媒体
- `arrow-undo`, `arrow-redo`, `arrow-right` - 箭头
- `search`, `add`, `document-edit`, `star`, `comment-note` - 操作
- `text-align-left`, `text-align-center`, `text-align-right`, `text-align-justify` - 对齐
- `text-bullet-list`, `text-number-list`, `task-list` - 列表
- `fullscreen-maximize`, `resize-small`, `line-horizontal` - 界面

### Antd Icons
- `link`, `rollback` - 导航
- `like`, `dislike`, `comment` - 交互

### Ionicons4
- `ios-code`, `ios-close` - 操作
- `ios-notifications-outline`, `ios-notifications-off` - 通知

### Tabler Icons
- `blockquote` - 引用

## 迁移指南

### 从vicons迁移

1. **替换导入**:
```typescript
// 原来
import { TextBold20Filled } from '@vicons/fluent';

// 现在
import Icon from '@/icons/Icon.vue';
// 或使用兼容组件
import VIcon from '@/icons/VIcon.vue';
```

2. **替换使用**:
```vue
<!-- 原来 -->
<NIcon>
  <TextBold20Filled />
</NIcon>

<!-- 现在 -->
<Icon name="text-bold" />
<!-- 或使用兼容组件 -->
<VIcon library="fluent" iconName="TextBold20Filled" />
```

### 批量替换脚本

可以使用以下正则表达式进行批量替换：

1. 查找: `<NIcon[^>]*>\s*<(\w+)\s*/>\s*</NIcon>`
2. 替换: `<VIcon library="fluent" iconName="$1" />`

## API

### Icon组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | IconName | - | 图标名称 |
| size | string \| number | '1em' | 图标尺寸 |
| color | string | 'currentColor' | 图标颜色 |
| clickable | boolean | false | 是否可点击 |

### VIcon组件

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| library | 'fluent' \| 'antd' \| 'ionicons4' \| 'tabler' | - | 图标库 |
| iconName | string | - | 原vicons图标名称 |
| size | string \| number | '1em' | 图标尺寸 |
| color | string | 'currentColor' | 图标颜色 |
| clickable | boolean | false | 是否可点击 |

### 工具函数

- `getIconSvg(name: IconName): string` - 获取SVG字符串
- `hasIcon(name: string): boolean` - 检查图标是否存在
- `createIconElement(name: IconName, options?): HTMLElement` - 创建图标DOM元素
- `getIconHtml(name: IconName, options?): string` - 获取图标HTML字符串
- `mapVIconToIconName(library: string, iconName: string): IconName | null` - 映射vicons图标名称

## 添加新图标

1. 在 `iconSvgs` 对象中添加新的SVG字符串
2. 如果是替换vicons图标，在对应的映射文件中添加映射关系
3. 更新README文档中的图标列表

## 项目结构

```
src/icons/
├── index.ts          # 核心图标定义和基础函数
├── types.ts          # TypeScript类型定义
├── mapping.ts        # vicons图标映射
├── utils.ts          # 工具函数（搜索、分类等）
├── config.ts         # 配置管理
├── cache.ts          # 缓存系统
├── install.ts        # Vue插件安装
├── main.ts           # 主入口文件
├── test.ts           # 测试文件
├── migrate.js        # 迁移脚本
├── Icon.vue          # 主图标组件
├── VIcon.vue         # vicons兼容组件
├── IconDemo.vue      # 演示组件
├── IconBrowser.vue   # 图标浏览器
├── package.json      # 包配置
└── README.md         # 文档
```

## 高级功能

### 配置系统
```typescript
import { updateConfig } from '@/icons/config';

updateConfig({
  defaultSize: '20px',
  defaultColor: '#1890ff',
  enableCache: true,
  debug: true
});
```

### 缓存系统
```typescript
import { clearIconCache, getIconCacheStats } from '@/icons/cache';

// 清空缓存
clearIconCache();

// 获取缓存统计
const stats = getIconCacheStats();
console.log(stats);
```

### 图标搜索
```typescript
import { searchIcons, getIconsByCategory } from '@/icons/utils';

// 搜索图标
const results = searchIcons('text');

// 按分类获取
const textIcons = getIconsByCategory('text');
```

## 测试

运行测试来验证图标系统功能：

```typescript
import { runIconSystemTests } from '@/icons/test';

const results = runIconSystemTests();
```

## 环境变量配置

可以通过环境变量配置图标系统：

```env
VITE_ICON_DEFAULT_SIZE=20px
VITE_ICON_DEFAULT_COLOR=#1890ff
VITE_ICON_DEBUG=true
VITE_ICON_CACHE_LIMIT=200
VITE_ICON_CSS_PREFIX=my-icon-
```
