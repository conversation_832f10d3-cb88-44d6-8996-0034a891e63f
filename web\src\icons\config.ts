// 图标系统配置

export interface IconConfig {
  // 默认尺寸
  defaultSize: string | number;
  // 默认颜色
  defaultColor: string;
  // 是否启用调试模式
  debug: boolean;
  // 是否启用缓存
  enableCache: boolean;
  // 缓存大小限制
  cacheLimit: number;
  // 是否在控制台显示警告
  showWarnings: boolean;
  // 自定义CSS类前缀
  cssPrefix: string;
  // 是否启用懒加载
  lazyLoad: boolean;
}

// 默认配置
export const defaultConfig: IconConfig = {
  defaultSize: '1em',
  defaultColor: 'currentColor',
  debug: false,
  enableCache: true,
  cacheLimit: 100,
  showWarnings: true,
  cssPrefix: 'icon-',
  lazyLoad: false,
};

// 当前配置
let currentConfig: IconConfig = { ...defaultConfig };

// 获取配置
export function getConfig(): IconConfig {
  return { ...currentConfig };
}

// 更新配置
export function updateConfig(config: Partial<IconConfig>): void {
  currentConfig = { ...currentConfig, ...config };
  
  if (currentConfig.debug) {
    console.log('图标系统配置已更新:', currentConfig);
  }
}

// 重置配置
export function resetConfig(): void {
  currentConfig = { ...defaultConfig };
}

// 配置验证
export function validateConfig(config: Partial<IconConfig>): boolean {
  try {
    if (config.defaultSize !== undefined) {
      if (typeof config.defaultSize !== 'string' && typeof config.defaultSize !== 'number') {
        throw new Error('defaultSize must be string or number');
      }
    }
    
    if (config.defaultColor !== undefined) {
      if (typeof config.defaultColor !== 'string') {
        throw new Error('defaultColor must be string');
      }
    }
    
    if (config.cacheLimit !== undefined) {
      if (typeof config.cacheLimit !== 'number' || config.cacheLimit < 0) {
        throw new Error('cacheLimit must be positive number');
      }
    }
    
    if (config.cssPrefix !== undefined) {
      if (typeof config.cssPrefix !== 'string') {
        throw new Error('cssPrefix must be string');
      }
    }
    
    return true;
  } catch (error) {
    if (currentConfig.showWarnings) {
      console.warn('图标配置验证失败:', error);
    }
    return false;
  }
}

// 从环境变量加载配置
export function loadConfigFromEnv(): void {
  const envConfig: Partial<IconConfig> = {};
  
  if (process.env.VITE_ICON_DEFAULT_SIZE) {
    envConfig.defaultSize = process.env.VITE_ICON_DEFAULT_SIZE;
  }
  
  if (process.env.VITE_ICON_DEFAULT_COLOR) {
    envConfig.defaultColor = process.env.VITE_ICON_DEFAULT_COLOR;
  }
  
  if (process.env.VITE_ICON_DEBUG) {
    envConfig.debug = process.env.VITE_ICON_DEBUG === 'true';
  }
  
  if (process.env.VITE_ICON_CACHE_LIMIT) {
    envConfig.cacheLimit = parseInt(process.env.VITE_ICON_CACHE_LIMIT, 10);
  }
  
  if (process.env.VITE_ICON_CSS_PREFIX) {
    envConfig.cssPrefix = process.env.VITE_ICON_CSS_PREFIX;
  }
  
  if (validateConfig(envConfig)) {
    updateConfig(envConfig);
  }
}

// 导出配置相关的工具函数
export {
  currentConfig as config,
};
