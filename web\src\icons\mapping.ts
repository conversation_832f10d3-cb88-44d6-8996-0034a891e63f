// vicons图标名称到新图标系统的映射
import type { IconName } from './index';

// vicons/fluent 映射
export const fluentIconMapping: Record<string, IconName> = {
  'TextHeader120Filled': 'text-header-1',
  'TextHeader220Filled': 'text-header-2', 
  'TextHeader320Filled': 'text-header-3',
  'TextBold20Filled': 'text-bold',
  'TextUnderline24Filled': 'text-underline',
  'TextStrikethrough20Filled': 'text-strikethrough',
  'Code20Filled': 'code',
  'Image28Regular': 'image',
  'ArrowUndo16Filled': 'arrow-undo',
  'ArrowRedo16Filled': 'arrow-redo',
  'LineHorizontal120Filled': 'line-horizontal',
  'VideoClip24Regular': 'video-clip',
  'FullScreenMaximize16Filled': 'fullscreen-maximize',
  'ResizeSmall20Filled': 'resize-small',
  'TextBulletListLtr16Filled': 'text-bullet-list',
  'TextNumberListLtr16Filled': 'text-number-list',
  'TaskListLtr24Filled': 'task-list',
  'TextAlignRight24Filled': 'text-align-right',
  'TextAlignLeft24Filled': 'text-align-left',
  'TextAlignCenter24Filled': 'text-align-center',
  'TextAlignJustify24Filled': 'text-align-justify',
  'Search24Regular': 'search',
  'Add24Regular': 'add',
  'DocumentEdit16Regular': 'document-edit',
  'Star48Regular': 'star',
  'CommentNote20Regular': 'comment-note',
  'ArrowRight20Filled': 'arrow-right',
};

// vicons/antd 映射
export const antdIconMapping: Record<string, IconName> = {
  'LinkOutlined': 'link',
  'RollbackOutlined': 'rollback',
  'LikeOutlined': 'like',
  'DislikeOutlined': 'dislike',
  'CommentOutlined': 'comment',
};

// vicons/ionicons4 映射
export const ionicons4Mapping: Record<string, IconName> = {
  'IosCode': 'ios-code',
  'IosNotificationsOutline': 'ios-notifications-outline',
  'IosNotificationsOff': 'ios-notifications-off',
  'IosClose': 'ios-close',
};

// vicons/tabler 映射
export const tablerIconMapping: Record<string, IconName> = {
  'Italic': 'text-italic',
  'Blockquote': 'blockquote',
};

// 统一映射函数
export function mapVIconToIconName(library: string, iconName: string): IconName | null {
  switch (library) {
    case 'fluent':
      return fluentIconMapping[iconName] || null;
    case 'antd':
      return antdIconMapping[iconName] || null;
    case 'ionicons4':
      return ionicons4Mapping[iconName] || null;
    case 'tabler':
      return tablerIconMapping[iconName] || null;
    default:
      return null;
  }
}

// 获取所有映射
export function getAllMappings() {
  return {
    fluent: fluentIconMapping,
    antd: antdIconMapping,
    ionicons4: ionicons4Mapping,
    tabler: tablerIconMapping,
  };
}
