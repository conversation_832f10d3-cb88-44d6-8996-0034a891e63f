<template>
  <div
    :class="{
      'user-comment-container-fixed': comment.fixed,
      'user-comment-container': !comment.fixed,
      'comment-flash': flashCommentId === comment.id,
    }"
  >
    <!-- 用户信息 -->
    <div class="user-info-row">
      <NAvatar
        round
        size="large"
        object-fit="cover"
        :src="comment.publisherAvatar ? fileApi.getResourceURL(comment.publisherAvatar) : ''"
      />
      <div class="user-detail-col">
        <span class="user-nickname">
          {{ comment.publisher }}
        </span>
        <span class="user-extra-info">
          {{ comment.publisherJob }} |
          <span class="time-clickable" @click="toggleTimeFormat(comment)">
            {{ comment.showExactTime ? comment.exactPublishedAt : comment.publishedAt }}
          </span>
          |
          {{ comment.ipLocation }}
        </span>
      </div>
    </div>

    <!-- 评论内容 -->
    <div class="comment-content-row">
      <TiptapEditor
        :file-bucket="COMMENT"
        v-model="comment.contentObj"
        :extensions="COMMENT_EXTENSIONS"
        :editable="false"
      />
    </div>

    <!-- 评论交互栏 - 使用拆分出的控制组件 -->
    <CommentControls
      :comment="comment"
      :showReplyListBtn="showReplyListBtn"
      @show-reply-list="$emit('showReplyList', comment)"
      @handle-comment-reply-click="$emit('handleCommentReplyClick', comment)"
      @interaction-btn="(comment, actionType) => $emit('interactionBtn', comment, actionType)"
      @favorite-btn="$emit('favoriteBtn', comment)"
    />

    <!-- 评论回复栏 -->
    <div class="comment-reply-row" v-show="commentInputVisible === comment.id">
      <TiptapEditor
        v-model="comment.quickCommentReply"
        class="comment-reply-tiptap-editor"
        :ref="(el) => el && updateEditor(comment.id, el)"
        :editor-props="{
          attributes: {
            class: 'ProseMirrorInput',
            'data-comment-id': comment.id,
          },
        }"
        :file-bucket="COMMENT"
        :placeholder="'说是你的自由，但是...'"
        :show-character-count="true"
        :extensions="editorExtensions"
        :toolbar="true"
        @keydown.alt.enter.prevent="$emit('quickReplyComment', comment)"
      />
      <NButton
        class="comment-reply-send-btn"
        text
        type="info"
        :loading="quickReplyLoading && comment.id ? quickReplyLoading.get(comment.id) : false"
        @click="$emit('quickReplyComment', comment)"
      >
        <NIcon size="28"><ArrowReplyDown24Filled /></NIcon>
      </NButton>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Icon from '@/icons/Icon.vue';


import { NAvatar, NButton, NIcon } from 'naive-ui'
import { computed, reactive } from 'vue'

import fileApi from '@/api/file'
import CommentControls from '@/components/comment/CommentControls.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { COMMENT } from '@/constants/bucket.constants'
import { COMMENT_EXTENSIONS } from '@/constants/tiptap.constants'
import type { Comment } from '@/types/comment.types'
import type { EditorWithFormatPainter } from '@/types/tiptap.types'

const props = defineProps<{
  comment: Comment
  flashCommentId: string
  showReplyListBtn: boolean
  commentInputVisible: string
  quickReplyLoading: Map<string, boolean>
}>()

const emit = defineEmits<{
  (e: 'showReplyList', comment: Comment): void
  (e: 'handleCommentReplyClick', comment: Comment): void
  (e: 'interactionBtn', comment: Comment, actionType: number): void
  (e: 'favoriteBtn', comment: Comment): void
  (e: 'quickReplyComment', comment: Comment): void
  (e: 'updateEditor', commentId: string, editor: EditorWithFormatPainter): void
}>()

const editorExtensions = [...COMMENT_EXTENSIONS, 'characterCount']

const updateEditor = (commentId: string, el: any) => {
  if (el && 'editor' in el) {
    emit('updateEditor', commentId, el.editor as EditorWithFormatPainter)
  }
}

// 切换时间显示格式
const toggleTimeFormat = (comment: Comment) => {
  if (comment.showExactTime === undefined) {
    comment.showExactTime = true
  } else {
    comment.showExactTime = !comment.showExactTime
  }
}
</script>

<style scoped lang="scss">
.user-comment-container {
  border: var(--border-1);
  border-radius: 0.6rem;
  padding: 0.6rem 1.25rem;
  margin-bottom: 3%;
  background-color: var(--comment-container-bg, var(--white-1));
}

.user-comment-container-fixed {
  border: var(--border-1);
  border-radius: 0.6rem;
  padding: 0.6rem 1.25rem;
  margin-bottom: 3%;
  background-color: var(--comment-fixed-bg, var(--blue-light));
  position: sticky;
  top: 0;
  z-index: 2;
  opacity: 1;
}

.comment-flash {
  animation: flash 1s ease-in-out;

  :deep(.tiptap-editor-wrapper),
  :deep(.editor-content),
  :deep(.ProseMirror),
  :deep(p),
  :deep(blockquote),
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    background-color: var(--blue-light) !important;
  }
}

/* 确保评论内容的背景色与评论容器一致 */
.user-comment-container-fixed,
.comment-flash {
  :deep(.tiptap-editor-wrapper),
  :deep(.editor-content),
  :deep(.ProseMirror),
  :deep(p),
  :deep(blockquote),
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    background-color: var(--comment-fixed-bg, var(--blue-light));
  }

  /* 确保回复框也继承蓝色背景 */
  .comment-reply-row {
    background-color: var(--comment-fixed-reply-bg, var(--blue-light));

    .comment-reply-tiptap-editor {
      :deep(.tiptap-editor-wrapper),
      :deep(.editor-content) {
        background-color: var(--comment-fixed-reply-bg, var(--blue-light));
      }

      :deep(.ProseMirrorInput) {
        background-color: var(--comment-fixed-prosemirror-bg, var(--blue-light));
        border: 1px solid var(--gray-3);
        opacity: 1; /* 确保不透明 */
      }
    }
  }
}

/* 确保普通评论内容的背景色与评论容器一致 */
.user-comment-container {
  :deep(.tiptap-editor-wrapper),
  :deep(.editor-content),
  :deep(.ProseMirror),
  :deep(p),
  :deep(blockquote),
  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    background-color: var(--comment-container-bg, var(--white-1));
  }

  /* 确保回复框也继承普通评论容器背景 */
  .comment-reply-row {
    background-color: var(--comment-reply-bg, var(--white-1));

    .comment-reply-tiptap-editor {
      :deep(.tiptap-editor-wrapper),
      :deep(.editor-content) {
        background-color: var(--comment-reply-bg, var(--white-1));
      }

      :deep(.ProseMirrorInput) {
        background-color: var(--comment-reply-prosemirror-bg, var(--white-1));
        border: 1px solid var(--gray-3);
      }
    }
  }
}

.user-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 0.6rem;

  .user-detail-col {
    margin-left: 0.6rem;

    .user-nickname {
      display: block;
      font-weight: bold;
    }

    .user-extra-info {
      display: block;

      .time-clickable {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

.comment-content-row {
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.comment-interaction-reply {
  display: flex;

  .comment-reply-info {
    display: flex;
    align-items: center;
    width: 60%;
    margin-right: 1rem;
    font-size: 0.8rem;
    gap: 0.3rem;

    .comment-reply-list-btn {
      margin-right: 1%;
    }
  }

  .comment-interaction-btn {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-size: 0.8rem;
  }
}

.comment-reply-row {
  display: flex;
  justify-content: center;
  align-items: end;
  background-color: inherit; /* 继承父容器背景色 */
  padding-top: 0.5rem;

  .comment-reply-tiptap-editor {
    max-width: 80%;
    margin-right: 1.25rem;
  }
}

.comment-reply-send-btn {
  margin-bottom: 1.5rem;
}

/* 覆盖固定回复框中的样式 */
.user-comment-container-fixed .comment-reply-send-btn,
.comment-flash .comment-reply-send-btn {
  background-color: var(--comment-fixed-reply-btn-bg, var(--blue-light));
  opacity: 1;
}

/* 覆盖普通评论回复框中的样式 */
.user-comment-container .comment-reply-send-btn {
  background-color: inherit; /* 继承父容器背景色，确保一致性 */
}

@keyframes flash {
  0% {
    background-color: var(--blue-light);
  }

  50% {
    background-color: var(--blue-light);
  }

  100% {
    background-color: var(--blue-light);
  }
}
</style>
