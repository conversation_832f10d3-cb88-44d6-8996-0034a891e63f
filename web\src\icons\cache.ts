// 图标缓存系统
import { getConfig } from './config';
import type { IconName } from './types';

// 缓存项接口
interface CacheItem {
  key: string;
  value: string | HTMLElement;
  timestamp: number;
  accessCount: number;
}

// 缓存管理器
class IconCache {
  private cache = new Map<string, CacheItem>();
  private maxSize: number;
  
  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }
  
  // 生成缓存键
  private generateKey(name: IconName, options?: any): string {
    const optionsStr = options ? JSON.stringify(options) : '';
    return `${name}:${optionsStr}`;
  }
  
  // 获取缓存
  get(name: IconName, options?: any): string | HTMLElement | null {
    const config = getConfig();
    if (!config.enableCache) return null;
    
    const key = this.generateKey(name, options);
    const item = this.cache.get(key);
    
    if (item) {
      // 更新访问信息
      item.accessCount++;
      item.timestamp = Date.now();
      
      if (config.debug) {
        console.log(`图标缓存命中: ${key}`);
      }
      
      return item.value;
    }
    
    return null;
  }
  
  // 设置缓存
  set(name: IconName, value: string | HTMLElement, options?: any): void {
    const config = getConfig();
    if (!config.enableCache) return;
    
    const key = this.generateKey(name, options);
    
    // 如果缓存已满，清理最少使用的项
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }
    
    const item: CacheItem = {
      key,
      value,
      timestamp: Date.now(),
      accessCount: 1,
    };
    
    this.cache.set(key, item);
    
    if (config.debug) {
      console.log(`图标已缓存: ${key}`);
    }
  }
  
  // 清理最少使用的缓存项
  private evictLeastUsed(): void {
    let leastUsedKey = '';
    let leastAccessCount = Infinity;
    let oldestTimestamp = Infinity;
    
    for (const [key, item] of this.cache) {
      if (item.accessCount < leastAccessCount || 
          (item.accessCount === leastAccessCount && item.timestamp < oldestTimestamp)) {
        leastUsedKey = key;
        leastAccessCount = item.accessCount;
        oldestTimestamp = item.timestamp;
      }
    }
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      
      const config = getConfig();
      if (config.debug) {
        console.log(`清理缓存项: ${leastUsedKey}`);
      }
    }
  }
  
  // 清空缓存
  clear(): void {
    this.cache.clear();
    
    const config = getConfig();
    if (config.debug) {
      console.log('图标缓存已清空');
    }
  }
  
  // 删除特定缓存
  delete(name: IconName, options?: any): boolean {
    const key = this.generateKey(name, options);
    const result = this.cache.delete(key);
    
    const config = getConfig();
    if (config.debug && result) {
      console.log(`删除缓存项: ${key}`);
    }
    
    return result;
  }
  
  // 获取缓存统计信息
  getStats() {
    const items = Array.from(this.cache.values());
    const totalAccess = items.reduce((sum, item) => sum + item.accessCount, 0);
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      totalAccess,
      averageAccess: items.length > 0 ? totalAccess / items.length : 0,
      oldestTimestamp: Math.min(...items.map(item => item.timestamp)),
      newestTimestamp: Math.max(...items.map(item => item.timestamp)),
    };
  }
  
  // 更新最大缓存大小
  setMaxSize(size: number): void {
    this.maxSize = size;
    
    // 如果当前缓存超过新的限制，清理多余的项
    while (this.cache.size > this.maxSize) {
      this.evictLeastUsed();
    }
  }
  
  // 获取所有缓存键
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }
  
  // 检查是否存在缓存
  has(name: IconName, options?: any): boolean {
    const key = this.generateKey(name, options);
    return this.cache.has(key);
  }
}

// 创建全局缓存实例
const iconCache = new IconCache();

// 导出缓存实例和相关函数
export { iconCache };

// 便捷的缓存操作函数
export function getCachedIcon(name: IconName, options?: any): string | HTMLElement | null {
  return iconCache.get(name, options);
}

export function setCachedIcon(name: IconName, value: string | HTMLElement, options?: any): void {
  iconCache.set(name, value, options);
}

export function clearIconCache(): void {
  iconCache.clear();
}

export function getIconCacheStats() {
  return iconCache.getStats();
}

export function updateCacheSize(size: number): void {
  iconCache.setMaxSize(size);
}

// 初始化缓存配置
export function initCache(): void {
  const config = getConfig();
  iconCache.setMaxSize(config.cacheLimit);
  
  if (config.debug) {
    console.log('图标缓存系统已初始化', {
      maxSize: config.cacheLimit,
      enabled: config.enableCache,
    });
  }
}
